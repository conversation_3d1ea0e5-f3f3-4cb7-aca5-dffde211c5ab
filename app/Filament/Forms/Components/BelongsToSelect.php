<?php

namespace App\Filament\Forms\Components;

use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Forms\Components\Select;
use Filament\Notifications\Notification;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BelongsToSelect extends Select
{
    protected bool | \Closure $showCreateAction = true;

    protected function setUp(): void
    {
        parent::setUp();

        $this->belowContent(
            function (BelongsToSelect $component) {
                if (!$component->isShowCreateAction()) {
                    return null;
                }

                $relationName = $component->getRelationshipName();
                $relatedModel = app($component->getModel())->{$relationName}()->getRelated();
                $resource = Filament::getModelResource(get_class($relatedModel));

                if (!$resource) {
                    return null;
                }

                return Action::make("create_{$relationName}")
                    ->label(__('Create new'))
                    ->model(get_class($relatedModel))
                    ->icon(Heroicon::OutlinedPlus)
                    ->schema($resource::form(new Schema())->getComponents())
                    ->action(function (array $data, $set) use ($relationName, $relatedModel, $component) {
                        try {
                            DB::beginTransaction();

                            $record = $relatedModel->fill($data);
                            $record->save();

                            $set($component->getName(), $record->id);

                            DB::commit();
                        }catch (\Exception $e) {
                            DB::rollBack();

                            Notification::make('create_failed')
                                ->title(__('Failed to create'))
                                ->danger()
                                ->send();

                            Log::error($e->getMessage(), [
                                'exception' => $e->getMessage(),
                                'trace' => $e->getTraceAsString(),
                            ]);
                        }
                    });
            }
        );
    }

    public function showCreateAction(bool | \Closure $showCreateAction = true): static
    {
        $this->showCreateAction = $showCreateAction;

        return $this;
    }

    public function isShowCreateAction(): bool
    {
        return $this->evaluate($this->showCreateAction);
    }
}
