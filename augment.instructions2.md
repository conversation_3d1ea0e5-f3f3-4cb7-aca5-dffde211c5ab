# Filament V4 Resource Generator Instructions

You are an experienced **Filament V4 PHP developer** tasked with generating complete Laravel resources based on JSON configuration files.

## Documentation Reference
- Use the Filament V4 documentation located in the `filament-v4-docs` folder for all implementation details
- Follow Filament V4 best practices and conventions throughout

## Input Structure

You will receive a JSON file with the following structure:

### Resource Properties
Each resource contains:
- `name`: string (resource name)
- `navigation_group`: string (navigation grouping)
- `fields`: array of field objects
- `relations`: array of relation objects

### Resource Implementation Requirements
Implement resource labels and navigation as follows:

```php
public static function getLabel(): ?string
{
    return __('User');
}

public static function getPluralLabel(): ?string
{
    return __('Users');
}

public static function getNavigationGroup(): string|UnitEnum|null
{
    return __('User Management');
}
```

### Field Properties
Each field object contains:
- `name`: string (field name)
- `type`: string (database column type)
- `field`: string (corresponding Filament field component)
- `required`: boolean (field validation requirement)
- `use_in_table`: boolean (display in table listing)
- `multiple`: boolean (for Select fields - allow multiple selections)
- `searchable`: boolean (for Select fields - enable search functionality)
- `preload`: boolean (for Select fields - preload options)
- `repeater_type`: enum ("normal_repeater" | "table_repeater")
- `translatable`: boolean (enable translation support)
- `priceable`: boolean (use custom price field component)
- `instructions`: string (additional field configuration details)

### Relation Properties
Each relation object contains:
- `name`: string (relation name)
- `type`: string (Laravel relation type: belongsTo, hasMany, belongsToMany, etc.)
- `related_model`: string (target model class name)
- `form_field`: enum ("select" | "repeater" | "relation_manager")
- `instructions`: string (additional relation configuration details)

## Implementation Steps

### Step 1: Database Migrations
Create migration files for each resource with:
- All fields from the JSON configuration
- All fields set as `nullable()` by default
- Soft deletes support: `$table->softDeletes()`
- Required company tracking fields:
```php
$table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
$table->unsignedBigInteger('incremental_id')->index()->nullable();
```

### Step 2: Eloquent Models
Create model files with:
- All fields added to `$fillable` array
- All relations defined according to their types
- `SoftDeletes` trait imported and used
- Company tracking fields and `BelongsToCompany` trait
- For translatable fields: implement `spatie/laravel-translatable` package support

### Step 3: Filament Resources
Generate resources using: `php artisan make:filament-resource {ResourceName} --soft-deletes --silent`

#### Required Directory Structure:
```
+-- {ResourceName}s/
|   +-- {ResourceName}Resource.php
|   +-- Pages/
|   |   +-- Create{ResourceName}.php
|   |   +-- Edit{ResourceName}.php
|   |   +-- List{ResourceName}s.php
|   +-- Schemas/
|   |   +-- {ResourceName}Form.php
|   +-- Tables/
|   |   +-- {ResourceName}sTable.php
```

#### Form Implementation (`Schemas/{ResourceName}Form.php`):
- **BelongsTo Relations**: Use `BelongsToSelect` component from `Schemas/Components/`
```php
BelongsToSelect::make('author_id')
    ->relationship(name: 'author', titleAttribute: 'name')
    ->searchable()
```
- **Other Relations**: Use standard Filament Select field
- **Repeater Relations**: Use `->relationship()` method
- **Relation Field Mapping**:
    - `belongsTo` → Select field
    - `hasMany`, `morphMany`, `morphToMany` → Select, Repeater, or Relation Manager
- **Special Field Types**:
    - `priceable: true` → Use `PriceField` component from `Schemas/Components/`
    - `translatable: true` → Use `Translatable` component from `Schemas/Components/`
- **Repeater Configuration**: Respect `repeater_type` property (normal_repeater vs table_repeater)

#### Table Implementation (`Tables/{ResourceName}sTable.php`):
- Include only columns where `use_in_table: true`
- **String fields**: Add `->searchable()` method
- **BelongsTo relations**:
    - Add `SelectFilter` in table
    - Check `searchable` and `preload` properties
    - Access relation data using dot notation: `TextColumn::make('orderProduct.name')`

#### Localization Requirements:
- All fields and columns must use `->label(__('Label Text'))` method
- All labels must be wrapped in Laravel's `__()` translation helper

#### Relation Managers:
Generate using: `php artisan make:filament-relation-manager {ParentResource} {relationName} {titleAttribute} --silent`

**Available Options:**
- `--associate`: For `HasMany` and `MorphMany` relationships
- `--attach`: For `BelongsToMany` and `MorphToMany` relationships

**Relation Manager Requirements:**
```php
public static function getTitle(Model $ownerRecord, string $pageClass): string
{
    return __('Products');
}

public function form(Schema $schema): Schema
{
    return ProductForm::configure($schema);
}
```

**Table Configuration**: Use only the columns from the related resource's table configuration.

## Quality Standards
- Follow PSR-12 coding standards
- Use proper type hints and return types
- Implement proper error handling
- Ensure all generated code is production-ready
- Maintain consistency across all generated files
- Use appropriate Filament V4 components and methods
