enhance this and write it in md file format and give me the file to download

i am using a ai code editor and i am giving it these insutrctions please enhance this

You are an experienced Filament V4 php developer

you will find the docs for filament v4 at filament-v4-docs folder please use it to get any information you need and follow the best practices

You will be given a json file with specific structure for filament resources and you are going to generate all related things to these resources

the json file will have the following structure:

list of resources, each resource has list of fields and relations

each resource has the following properties:
name: string
navigation_group: string
[filament_resource_generator_prompt.md](../../../Downloads/filament_resource_generator_prompt.md)
and should be implemented like this for example:

    public static function getLabel(): ?string
    {
        return __('User');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Users');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('User Management');
    }

each resource has list of fields and relations: 

 - each field has the following properties:

name: string
type: which will be database type
field: which will corespond to filament field
required: boolean to indicate if the field is required or not
use_in_table: boolean to indicate if the field should be shown in table or not
multiple: boolean to indicate if the field is multiple or not in case of the field is Select
searchable: boolean to indicate if the field is searchable or not for the select field
preload: boolean to indicate if the field should be preloaded or not for the select field
repeater_type: Select of 2 options (normal_repeater or table_repeater)
translateable: boolean to indicate if the field is translateable or not
priceable: boolean to indicate if the field is priceable or not
instructions: description to give you more details about the field. for example if the field is a radio field the instructions will have the options for the select field

 - each relation has the following properties:

name: string
type: which will be laravel relation type like (belongsTo, hasMany, etc)
related_model: which will be the related model name
form_field: which will be the form field type like (select, repeater, or relation manager)
instructions: string to give you more details about the relation

- in case of relation manager
  add the label function for example
  public static function getTitle(Model $ownerRecord, string $pageClass): string
  {
    return __('Products');
  }

also use the form schema of the related resource

like this

    public function form(Schema $schema): Schema
    {
        return ProductForm::configure($schema);
    }

and the for the table get columns from the related resource table. only the columns


you are going to follow these steps

1 - create database migration file for each resource and all fields must be added to the migration file and nullable and each migration should have soft deletes
also each migration must have company_id and incremental_id like this

            $table->foreignId('company_id')->nullable()->constrained()->nullOnDelete();
            $table->unsignedBigInteger('incremental_id')->index()->nullable();

2 - create model for each resource and all fields must be added to the model and fillable and all relations must be added to the model also must use the soft deletes trait
also add the company_id and incremental_id to the model and use the BelongsToCompany trait
if the field is translateable use the spatie/laravel-translatable package to make it translateable

3 - create filament resource with soft-deletes option using this command (php artisan make:filament-resource Customer --soft-deletes --silent) for example
with each resource you will have the following structure for example

+-- Customers
|   +-- CustomerResource.php
|   +-- Pages
|   |   +-- CreateCustomer.php
|   |   +-- EditCustomer.php
|   |   +-- ListCustomers.php
|   +-- Schemas
|   |   +-- CustomerForm.php
|   +-- Tables
|   |   +-- CustomersTable.php

- for the resource form you will add form fields in Form file 
    - for belongs to relation fields that will be on the form it self must use the ->relationship() method with the field like this and use BelongsToSelect component from the Schemas/Components folder
      BelongsToSelect::make('author_id')
      ->relationship(name: 'author', titleAttribute: 'name')
      ->searchable()
    and for other relations that will use the select field you must use default select field from filament
    - if the relation field is repeater also use the ->relationship() method
    - in case the relation type is belongsTo the form field will be select
    - in case the relation type is hasMany, morphMany, or morphToMany the form field will be select, repeater or relation manager
    - if your going to use the repeater field whether for relation or not take care of the repeater_type
    - if the field is priceable use the PriceField component from the Schemas/Components folder[filament_resource_generator_prompt.md](../../../Downloads/filament_resource_generator_prompt.md)
    - if the field is translateable use the Translateable component from the Schemas/Components folder

- for table columns you will add the columns that has use_in_table property set to true
  - for string fields will be ->searchable() in the table
  - for belongs to relation it will have a SelectFilter in the table and check if is searchable and preloaded like the form field
  - if its belongs to relation to get the value from the relation use the '.' like this for example
    TextColumn::make('orderProduct.name')

all fields and columns must have the ->label() method and the label must be translated using __()

for relation manager use this command for example

php artisan make:filament-relation-manager UserResource products name --silent
  --associate                                      Include associate actions in the table for `HasMany` and `MorphMany` relationships
  --attach                                         Include attach actions in the table for `BelongsToMany` and `MorphToMany` relationships
[filament_resource_generator_prompt.md](../../../Downloads/filament_resource_generator_prompt.md)
